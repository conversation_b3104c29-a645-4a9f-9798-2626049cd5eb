{"name": "osoa_h5", "version": "1.0.8", "description": "thinkive hvue h5Frame CLI", "author": "thinkive h5 frame group", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build": "node build/build.js", "test": "node build/build.js -- test", "uat": "node build/build.js -- uat", "pdt": "node build/build.js -- pdt", "test-mobile": "node build/build.js test mobile", "test-telecom": "node build/build.js test telecom", "test-unicom": "node build/build.js test unicom", "build-mobile": "node build/build.js pdt mobile", "build-telecom": "node build/build.js pdt telecom", "build-unicom": "node build/build.js pdt unicom"}, "dependencies": {"axios": "^0.18.0", "babel-polyfill": "^6.26.0", "better-scroll": "^1.15.2", "crypto-js": "^3.1.9-1", "echarts": "^4.6.0", "exif-js": "^2.3.0", "fastclick-hvue": "^1.0.7", "filter": "^0.1.1", "image-webpack-loader": "^8.1.0", "pinyin-match": "^1.1.0", "thinkive-hui": "^1.0.26", "thinkive-hvue": "^1.1.7", "v-distpicker-q": "^1.2.10", "vant": "^2.4.1", "vue": "^2.5.2", "vue-bus": "^1.2.1", "vue-router": "^2.7.0", "vuex": "^3.1.0"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "compression-webpack-plugin": "^1.1.12", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "eslint": "^4.15.0", "eslint-config-prettier": "^10.1.5", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^3.0.0", "eslint-loader": "^1.7.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "node-notifier": "^5.1.2", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vconsole": "^3.3.0", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "main": ".eslintrc.js", "license": "ISC"}