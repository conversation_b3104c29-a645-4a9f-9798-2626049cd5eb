#!/usr/bin/env node

/**
 * 验证构建配置脚本
 * 用于验证不同运营商和环境的构建配置是否正确应用
 */

const fs = require('fs')
const path = require('path')

// 运营商配置映射
const operatorConfigs = {
  test: {
    mobile: {
      serviceType: '0',
      serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906',
      imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    telecom: {
      serviceType: '1',
      serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906',
      imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    unicom: {
      serviceType: '2',
      serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906',
      imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id='
    }
  },
  production: {
    mobile: {
      serviceType: '0',
      serviceTypeUrl: 'spyd.dtsbc.com.cn:8906',
      imgUrl: 'https://khyd.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    telecom: {
      serviceType: '1',
      serviceTypeUrl: 'spdx.dtsbc.com.cn:8906',
      imgUrl: 'https://khdx.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    unicom: {
      serviceType: '2',
      serviceTypeUrl: 'splt.dtsbc.com.cn:8906',
      imgUrl: 'https://khlt.dtsbc.com.cn:22203/picServlet?upload_id='
    }
  }
}

function verifyBuild(env, operator) {
  const envDir = env === 'test' ? 'dist_test' : 'dist_pdt'
  const pkgName = operator === 'telecom' ? 'osoa_h5' : `osoa_h5_${operator}`
  const buildPath = path.join(envDir, pkgName)
  
  console.log(`\n验证 ${env} 环境 ${operator} 运营商构建...`)
  console.log(`构建路径: ${buildPath}`)
  
  // 检查目录是否存在
  if (!fs.existsSync(buildPath)) {
    console.log(`❌ 构建目录不存在: ${buildPath}`)
    return false
  }
  
  console.log(`✅ 构建目录存在: ${buildPath}`)
  
  // 检查views目录
  const viewsPath = path.join(buildPath, 'views')
  if (!fs.existsSync(viewsPath)) {
    console.log(`❌ views目录不存在: ${viewsPath}`)
    return false
  }
  
  console.log(`✅ views目录存在: ${viewsPath}`)
  
  // 检查index.html
  const indexPath = path.join(viewsPath, 'index.html')
  if (!fs.existsSync(indexPath)) {
    console.log(`❌ index.html不存在: ${indexPath}`)
    return false
  }
  
  console.log(`✅ index.html存在: ${indexPath}`)
  
  // 检查静态资源目录
  const staticPath = path.join(viewsPath, 'static')
  if (!fs.existsSync(staticPath)) {
    console.log(`❌ static目录不存在: ${staticPath}`)
    return false
  }
  
  console.log(`✅ static目录存在: ${staticPath}`)
  
  return true
}

function main() {
  console.log('开始验证构建结果...')
  
  const testCases = [
    ['test', 'mobile'],
    ['test', 'telecom'],
    ['test', 'unicom'],
    ['production', 'mobile'],
    ['production', 'telecom'],
    ['production', 'unicom']
  ]
  
  let successCount = 0
  let totalCount = testCases.length
  
  testCases.forEach(([env, operator]) => {
    if (verifyBuild(env, operator)) {
      successCount++
    }
  })
  
  console.log(`\n验证完成: ${successCount}/${totalCount} 个构建成功`)
  
  if (successCount === totalCount) {
    console.log('🎉 所有构建验证通过！')
  } else {
    console.log('⚠️  部分构建验证失败，请检查构建过程')
  }
}

if (require.main === module) {
  main()
}

module.exports = { verifyBuild }
