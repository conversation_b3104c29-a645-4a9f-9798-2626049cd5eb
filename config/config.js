// 基础配置
const baseConfig = {
  // 请求签名所需参数对象
  ssoSign: {
    merchant_id: 'financeweb', // 商户id
    // 后端给的加签key再用signEncrypt方法加密后的key
    sign_key: 'OdDd+M4OHraSurI5/yRW3i1DohAZihnuvYOlohQ3r1DjyFimJqJy1w==',
    corp_id: '101000000001', // 统一账户公司编号
    app_id: '102000000001', // 统一账户应用编号
    encrypt_mode: '', // 请求参数加密模式: aes, des, base64. 默认base64.
    encrypt_key: '',
    sign_version: '1.0' // 加签名方法的版本，可与后端确定版本号。version: '1.0' or '2.0'
  },
  ssoSignNew: {
    sign_key: 'm53TzVkpa0OB4+zAawoUDxf+gYmPfy77/5HYbJ6oKHEKcZ264wPVjw==',
    company_id: 'THINKIVE', // 公司id
    system_id: 'TAMP', // 系统id
    encrypt_mode: 'none', // 请求参数加密模式: aes, des, base64, none. 默认base64
    encrypt_key: '',
    system_version: '1.0.2', // 系统版本, 这个值固定 '1.0.2'
    sign_version: '2.0' // 加签名方法的版本，可与后端确定版本号。version: '1.0' or '2.0'
  },
  sm4Key: 'f0f91d79220c5ec9f3c013a5b4499cb2', // 加密秘钥
  // session数据保存到app内存中, true/false, 默认false
  sessionSaveToApp: false,
  // 开启session数据保存到app内存中选项后，需要配置此项来过滤存储的key,
  // 配置在数组中key才会存在app内存中
  sessionKeyToApp: [],

  useTYSP: '1', // 是否使用统一视频，1是0否
  videoType: '0', // 视频类型 0tchat 1anychat
  ocrQuality: '1' // 是否开启拍照质检
}

// 运营商配置
const operatorConfigs = {
  // 测试环境配置
  test: {
    mobile: {
      serviceType: '0', // 移动
      serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906',
      imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    telecom: {
      serviceType: '1', // 电信
      serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906',
      imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    unicom: {
      serviceType: '2', // 联通
      serviceTypeUrl: 'h5sdsp.dtsbc.com.cn:8906',
      imgUrl: 'https://h5sdkh.dtsbc.com.cn:22203/picServlet?upload_id='
    }
  },
  // 生产环境配置
  production: {
    mobile: {
      serviceType: '0', // 移动
      serviceTypeUrl: 'spyd.dtsbc.com.cn:8906',
      imgUrl: 'https://khyd.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    telecom: {
      serviceType: '1', // 电信
      serviceTypeUrl: 'spdx.dtsbc.com.cn:8906',
      imgUrl: 'https://khdx.dtsbc.com.cn:22203/picServlet?upload_id='
    },
    unicom: {
      serviceType: '2', // 联通
      serviceTypeUrl: 'splt.dtsbc.com.cn:8906',
      imgUrl: 'https://khlt.dtsbc.com.cn:22203/picServlet?upload_id='
    }
  }
}

// 获取当前环境和运营商配置
function getCurrentConfig() {
  // 从环境变量中获取构建参数
  const buildEnv = process.env.BUILD_ENV || 'test'
  const operator = process.env.BUILD_OPERATOR || 'telecom'

  // 合并基础配置和运营商配置
  const operatorConfig = operatorConfigs[buildEnv] && operatorConfigs[buildEnv][operator]
    ? operatorConfigs[buildEnv][operator]
    : operatorConfigs.test.telecom // 默认配置

  return {
    ...baseConfig,
    ...operatorConfig
  }
}

const config = getCurrentConfig()

window.$hvue = {
  config
}
export default config
