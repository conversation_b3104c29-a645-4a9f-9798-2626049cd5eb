/**************************************************************
 @description vue过滤器常用方法
******************************************************************/
/**
 * 根据账户状态展示对应的样式
 * @param {*} holderStatus 账户状态 0  正常 1  冻结 2  挂失 3  销户 4  未确定 C  中登休眠 D  风险处置 F  系统锁定 G  内部休眠 L  未激活 S  休眠
 * @param {*} regFlag 指定状态 0 未指定 1 指定 2 新指定 3 指定中 4 撤指中 5 新指撤指中
 * @param {*} openStatus （权限）开通状态 0 未开通 1 已开通  2不满足
 */
const filAccountClass = (holderStatus, regFlag, openStatus) => {
  holderStatus = holderStatus || '0'
  regFlag = regFlag || '1'
  openStatus = openStatus || '0'
  let className = 'normal_span' // 正常
  if (openStatus === '1') {
    className = 'assign_span'
  } else if (openStatus === '0') {
    className = 'normal_span'
  } else {
    if (holderStatus !== '0') {
      className = 'dormant_span'
    } else {
      if (regFlag !== '1' && regFlag !== '2') {
        className = 'assign_span'
      }
    }
  }
  return className
}
/**
 * 根据账户状态展示对应的状态
 * @param {*} holderStatus 账户状态 0  正常 1  冻结 2  挂失 3  销户 4  未确定 C  中登休眠 D  风险处置 F  系统锁定 G  内部休眠 L  未激活 S  休眠
 * @param {*} regFlag 指定状态 0 未指定 1 指定 2 新指定 3 指定中 4 撤指中 5 新指撤指中
 * @param {*} openStatus （权限）开通状态 0 未开通 1 已开通  2不满足
 */
const filAccountState = (holderStatus, regFlag, openStatus, holderName = '') => {
  holderStatus = holderStatus || '0'
  regFlag = regFlag || '1'
  openStatus = openStatus || '0'
  let stateName = '正常'
  if (openStatus === '2' && holderStatus === '2' && holderName !== '') {
    return holderName
  }
  if (openStatus === '1') {
    stateName = '已开通'
  } else if (openStatus === '0') {
    stateName = '正常'
  } else {
    if (holderStatus !== '0') {
      stateName = '异常'
    } else {
      if (regFlag !== '1' && regFlag !== '2') {
        stateName = '未指定'
      }
    }
  }
  return stateName
}

/**
 * 显示风险等级是否过期
 * @param {风险等级} level
 * @param {是否过期} isEffective
 */
const fillRiskLevel = (level, isEffective) => {
  if (isEffective === '0') {
    return '已过期'
  } else {
    return level
  }
}

/* 数据为空的话填充0 */
const fillZero = value => {
  if (value === '') {
    return '0'
  } else {
    return value
  }
}
/* 数据为空的话填充-- */
const fillLine = value => {
  if (value === '' || value === null || value.trim() === null) {
    return '--'
  } else {
    return value
  }
}
/** 根据账户办理状态显示对应的账户样式 */
const fillStateStyle = value => {
  if (value === '0') {
    return 'ing'
  } else if (value === '1') {
    return 'ok'
  } else {
    return 'error'
  }
}
/** 根据账户办理状态显示对应的结果描述 */
const fillStateDesc = value => {
  if (value === '0') {
    return '处理中...'
  } else if (value === '1') {
    return '办理成功'
  } else if (value === '2') {
    return '办理失败'
  } else {
    return value
  }
}

/** 根据账户表单状态显示对应的办理结果样式 */
const formatFormStateStyle = value => {
  if (value === '2' || value === '3') {
    return 'ok'
  } else if (value === '4') {
    return 'error'
  } else {
    return 'ing'
  }
}
/** 表单状态转换成办理状态 */
// 表单状态 0:表单未提交完(暂存)     2:表单已处理  3:结束     4:驳回  8：审核  9待跑批（审核通过）
const formatFormStateDesc = (value, handleStatus) => {
  if (value === '0') {
    return '办理中'
  } else if (value === '1') {
    return '处理中'
  } else if (value === '2' || value === '3') {
    if (handleStatus == '1') {
      return '办理成功'
    } else if (handleStatus == '') {
      return '办理失败'
    } else {
      return '未处理'
    }
  } else if (value === '4') {
    return '审核驳回'
  } else if (value === '5') {
    return '放弃办理'
  } else if (value === '8') {
    return '待审核'
  } else if (value === '9') {
    return '待跑批'
  }
}

/** 根据账户类型展示对应的描述 */
const fillAccountType = (value, trdacctExcls) => {
  if (value === '深A') {
    return trdacctExcls == '2' ? '深圳' : '深A'
  } else if (value === '沪A') {
    return trdacctExcls == '2' ? '上海' : '沪A'
  } else {
    return value
  }
}
/** 根据rejectCode展示对应中文描述 */
const fillRejectCode = value => {
  if (value === '207') {
    return '身份证人像页照片认证未通过'
  } else if (value === '208') {
    return '身份证国徽页照片认证未通过'
  } else if (value === '209') {
    return '免冠照认证未通过'
  } else if (value === 'video') {
    return '视频认证未通过'
  } else if (value === 'blockTrade' || value === 'blockTradeInstitution') {
    return '大宗交易申请未通过原因：'
  } else if (value === 'recycleList') {
    return '固收平台申请未通过原因：'
  } else {
    return value
  }
}
/**
 * 金额格式化
 * @param {数值} num
 */
const formatMoney = num => {
  if (!num) {
    return '0.00'
  };
  let info = parseFloat(num).toFixed(2).toString().split('.')
  num = info[0]
  let result = ''
  while (num.length > 3) {
    result = ',' + num.slice(-3) + result
    num = num.slice(0, num.length - 3)
  }
  if (num) {
    result = num + result
  }
  info[0] = result
  return info.join('.')
}

const formatIdno = value => {
  if (!value) {
    return '--'
  } else {
    let str = value.substr(0, 6) + '*****' + value.substr(value.length - 4, value.length)
    return str
  }
}

const formatMobileNo = value => {
  if (value === '' || value === null) {
    return '--'
  } else {
    let str = value.substr(0, 3) + '*****' + value.substr(value.length - 4, value.length)
    return str
  }
}

const formatBankCardNo = value => {
  if (value === '' || value === null) {
    return '--'
  } else {
    let str = value.substr(0, 4) + ' **** ****' + value.substr(value.length - 4, value.length)
    return str
  }
}

const formatDate = (date, format) => {
  if (date === '********') {
    return '长期'
  }
  if (date === '') {
    return '--'
  }
  let TimeDate
  if (typeof (date) === 'string') {
    TimeDate = new Date()
    TimeDate.setFullYear(date.substr(0, 4), date.substr(4, 2) - 1, date.substr(6))
  }
  if ($h.isDate(date)) {
    TimeDate = date
  }
  if (!TimeDate || TimeDate === 'Invalid Date') {
    // alert("当前传入时间格式存在问题");
    return '--'
  } else {
    return TimeDate.format(format)
  }
}
Date.prototype.format = function (format) {
  let o = {
    'M+': this.getMonth() + 1, // month
    'd+': this.getDate(), // day
    'h+': this.getHours(), // hour
    'm+': this.getMinutes(), // minute
    's+': this.getSeconds(), // second
    'q+': Math.floor((this.getMonth() + 3) / 3), // quarter
    'S': this.getMilliseconds(), // millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    }
  }
  return format
}

/**
 * 根据账户类型值展示账户类型
 * @param {账户类型值} cls
 */
const getAccountType = cls => {
  switch (cls) {
    case '0':
      return '股东账户'
      break
    case '2':
      return '信用账户'
      break
    case '3':
      return '机构信用账户'
      break
    case '4':
      return '个人基金账户'
      break
    case '5':
      return '机构基金账户'
      break
    case '6':
      return '产品普通账户'
      break
    case '7':
      return '产品基金账户'
      break
    case '8':
      return '产品信用账户'
      break
    case '9':
      return '境外账户'
      break
    default:
      return '账户'
      break
  }
}

/**
 * 根据账户状态展示对应的状态
 * @param {*} holderStatus 账户状态 0  正常 1  冻结 2  挂失 3  销户 4  未确定 C  中登休眠 D  风险处置 F  系统锁定 G  内部休眠 L  未激活 S  休眠
 * @param {*} regFlag 指定状态 0 未指定 1 指定 2 新指定 3 指定中 4 撤指中 5 新指撤指中
 * @param {*} openStatus （权限）开通状态 0 未开通 1 已开通  2不满足
 */
const filterAccStatus = (holderStatus) => {
    switch (holderStatus) {
        case '0':
            return '正常';
            break;
        case '1':
            return '冻结';
            break;
        case '2':
            return '挂失';
            break;
        case '3':
            return '销户';
            break;
        case '4':
            return '未确定';
            break;
        case 'C':
            return '中登休眠';
            break;
        case 'D':
            return '风险处置';
            break;
        case 'F':
            return '系统锁定';
            break;
        case 'G':
            return '内部休眠';
            break;
        case 'L':
            return '未激活';
            break;
        case 'S':
            return '休眠';
            break;
        default:
            return '未知';
            break;
    }
}
/**
 * 根据日期只截图到年月日
 */
const formatDateYMD = (date) => {
  let formatdate = date.split(' ');
  return formatdate[0];
}

export {
  filAccountClass,
  filAccountState,
  fillRiskLevel,
  fillZero,
  fillStateStyle,
  fillStateDesc,
  filterAccStatus,

  formatFormStateStyle,
  formatFormStateDesc,
  fillAccountType,
  fillRejectCode,
  formatMoney,
  fillLine,
  formatIdno,
  formatDate,
  formatMobileNo,
  formatBankCardNo,
  getAccountType,
  formatDateYMD,
}
