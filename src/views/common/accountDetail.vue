<!--资质查询账户详情页面-->
<template>
  <article class="content">
    <headComponent headerTitle="资质查询"></headComponent>
    <div class="cond_box">
      <ul>
        <li
          :class="{ ok: openAccountFlag != '0', error: openAccountFlag == '0' }"
          v-if="accountListHA.length > 0"
        >
          <div class="tit">
            <p>已开通沪A股东账户</p>
          </div>
          <div class="cont">
            <p v-for="(item, index) in accountListHA" :key="index">
              <span
                >{{
                  item.market == '10' && item.trdacctExcls == '4'
                    ? '沪基金'
                    : item.market == '00' && item.trdacctExcls == '4'
                    ? '深基金'
                    : item.marketName
                }}
                {{ item.stockAccount }}</span
              >
              <em
                :class="
                  item.holderStatus
                    | filAccountClass(item.regFlag, item.openStatus)
                "
                >{{
                  item.holderStatus
                    | filAccountState(item.regFlag, item.openStatus, item.holderName)
                }}</em
              >
            </p>
          </div>
        </li>
        <li
          :class="{ ok: openAccountFlag != '0', error: openAccountFlag == '0' }"
          v-if="accountListSA.length > 0"
        >
          <div class="tit">
            <p>已开通深A股东账户</p>
          </div>
          <div class="cont">
            <p v-for="(item, index) in accountListSA" :key="index">
              <span
                >{{
                  item.market == '10' && item.trdacctExcls == '4'
                    ? '沪基金'
                    : item.market == '00' && item.trdacctExcls == '4'
                    ? '深基金'
                    : item.marketName
                }}
                {{ item.stockAccount }}</span
              >
              <em
                :class="
                  item.holderStatus
                    | filAccountClass(item.regFlag, item.openStatus)
                "
                >{{
                  item.holderStatus
                    | filAccountState(item.regFlag, item.openStatus)
                }}</em
              >
            </p>
          </div>
        </li>
        <li
          :class="{ ok: openAccountFlag != '0', error: openAccountFlag == '0' }"
          v-if="accountListXY.length > 0"
        >
          <div class="tit">
            <p>已开通信用账户</p>
          </div>
          <div class="cont">
            <p v-for="(item, index) in accountListXY" :key="index">
              <span
                >{{
                  item.market == '10' && item.trdacctExcls == '4'
                    ? '沪基金'
                    : item.market == '00' && item.trdacctExcls == '4'
                    ? '深基金'
                    : item.marketName
                }}
                {{ item.stockAccount }}</span
              >
              <em
                :class="
                  item.holderStatus
                    | filAccountClass(item.regFlag, item.openStatus)
                "
                >{{
                  item.holderStatus
                    | filAccountState(item.regFlag, item.openStatus)
                }}</em
              >
            </p>
          </div>
        </li>
      </ul>
    </div>
  </article>
</template>
<script>
import headComponent from '@/components/headComponent'
export default {
  components: { headComponent },
  data() {
    return {
      accountList: JSON.parse(this.$route.query.accountList),
      accountListSA: [],
      accountListHA: [],
      accountListXY: [],
      openAccountFlag: this.$route.query.openAccountFlag,
    }
  },
  created() {
    this.accountListSA = []
    this.accountListHA = []
    this.accountListXY = []
    // 处理出深护两个结果集
    for (let i = 0; i < this.accountList.length; i++) {
      const el = this.accountList[i]
      if (el.market == '00') {
        // 深A
        this.accountListSA.push(el)
      } else if (el.market == '10') {
        // 沪A
        this.accountListHA.push(el)
      } else if (el.market == '14' || el.market == '24') {
        // 信用
        this.accountListXY.push(el)
      }
    }
  },
  mounted() {
    window.phoneBackBtnCallBack = this.pageBack
  },
  methods: {
    pageBack() {
      this.$router.go(-1)
    },
  },
}
</script>
