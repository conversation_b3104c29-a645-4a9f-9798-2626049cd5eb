/*
 * @Author: chenjm
 * @Date: 2025-05-15 14:36:43
 * @LastEditors: chenjm
 * @LastEditTime: 2025-08-02 13:43:38
 * @Description:
 */
'use strict'
require('./check-versions')()

process.env.NODE_ENV = 'production'

// 解析命令行参数
const args = process.argv.slice(2)
let buildEnv = 'pdt' // 默认生产环境
let operator = 'telecom' // 默认电信

// 解析环境参数
if (args.length > 0) {
  buildEnv = args[0]
}

// 解析运营商参数
if (args.length > 1) {
  operator = args[1]
}

// 设置环境变量供配置文件使用
if (buildEnv === 'test') {
  process.env.BUILD_ENV = 'test'
} else {
  process.env.BUILD_ENV = 'production'
}
process.env.BUILD_OPERATOR = operator

// 为了兼容现有的config/index.js解析逻辑，设置npm_config_argv
const mockCommand = buildEnv === 'test' ? `test-${operator}` : `build-${operator}`
process.env.npm_config_argv = JSON.stringify({
  cooked: ['npm', mockCommand]
})

const ora = require('ora')
const rm = require('rimraf')
const path = require('path')
const chalk = require('chalk')
const webpack = require('webpack')
const config = require('../config')
const webpackConfig = require('./webpack.prod.conf')

const operatorNames = {
  mobile: '移动',
  telecom: '电信',
  unicom: '联通'
}

const envNames = {
  test: '测试',
  uat: '准生产',
  pdt: '生产'
}

const spinner = ora(`building for ${envNames[buildEnv] || buildEnv} ${operatorNames[operator] || operator}...`)
spinner.start()

rm(path.join(config.build.assetsRoot, config.build.assetsSubDirectory), err => {
  if (err) throw err
  webpack(webpackConfig, (err, stats) => {
    spinner.stop()
    if (err) throw err
    process.stdout.write(stats.toString({
      colors: true,
      modules: false,
      children: false, // If you are using ts-loader, setting this to true will make TypeScript errors show up during build.
      chunks: false,
      chunkModules: false
    }) + '\n\n')

    if (stats.hasErrors()) {
      console.log(chalk.red('  Build failed with errors.\n'))
      process.exit(1)
    }

    console.log(chalk.cyan('  Build complete.\n'))
    console.log(chalk.yellow(
      '  Tip: built files are meant to be served over an HTTP server.\n' +
      '  Opening index.html over file:// won\'t work.\n'
    ))
  })
})
